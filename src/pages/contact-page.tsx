import { useState, useMemo } from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from 'src/lib/components/ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from 'src/lib/components/ui/card';
import { Label } from 'src/lib/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from 'src/lib/components/ui/select';
import { Input } from 'src/lib/components/ui/input';
import { Button } from 'src/lib/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormNavigation } from 'src/shared/form-navigation';
import { phoneFieldSchema } from 'src/validators/phone-field-schema';
import { EventRegistrationContactType } from 'src/generated/api/dsv-public/model';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from 'src/lib/components/ui/dialog';

const contactSchema = (t: any) =>
  z.object({
    contacts: z.array(
      z.object({
        type: z.string().min(1, t('page.contact.error.type-required')),
        firstName: z
          .string({
            message: t('page.contact.error.first-name-required'),
          })
          .min(1, t('page.contact.error.first-name-required'))
          .max(32, t('validations.max-length', { maxLength: 32 })),
        lastName: z
          .string({
            message: t('page.contact.error.last-name-required'),
          })
          .min(1, t('page.contact.error.last-name-required'))
          .max(32, t('validations.max-length', { maxLength: 32 })),
        mobile: phoneFieldSchema({ required: true, maxLength: 32 }),
        email: z.string().email(t('page.contact.error.email-format')).optional(),
      })
    ),
  });

type ContactFormValues = z.infer<ReturnType<typeof contactSchema>>;

export function ContactPage() {
  const { form, hasUnderageParticipant, updateContactValue } = useFormContext();
  const contacts = useFormProgressStore(state => state.contacts);
  const addContact = useFormProgressStore(state => state.addContact);
  const removeContact = useFormProgressStore(state => state.removeContact);
  const isUnderageForm = form.underageCheckEnabled && hasUnderageParticipant;
  const { t } = useTranslation();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<number | null>(null);
  const [openContact, setOpenContact] = useState<string | undefined>(`contact-0`);

  const {
    formState: { errors },
    register,
    setValue,
    clearErrors,
  } = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema(t)),
    defaultValues: { contacts },
    mode: 'onTouched',
  });

  const handleUpdateContact = (index: number, field: string, value: any) => {
    // @ts-ignore
    setValue(`contacts.${index}.${field}`, value);
    updateContactValue(index, field, value);
  };

  const handleAddContact = () => {
    addContact();

    const newContactIndex = contacts.length;
    setOpenContact(`contact-${newContactIndex}`);
    setTimeout(() => {
      const newFirstNameInput = document.getElementById(`contacts.${newContactIndex}.firstName`);
      if (newFirstNameInput) {
        (newFirstNameInput as HTMLInputElement).focus();
      }
    }, 100);
  };

  const areRequiredFieldsFilled = useMemo(() => {
    return contacts.every(contact => {
      return (
        contact.type &&
        contact.firstName &&
        contact.lastName &&
        contact.mobile &&
        (contact.type !== EventRegistrationContactType.PARENT || contact.email)
      );
    });
  }, [contacts]);

  const handleRemoveContact = (index: number, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent accordion from toggling
    if (index > 0) {
      // Prevent deleting the first contact
      setContactToDelete(index);
      setDeleteDialogOpen(true);
    }
  };

  const confirmDelete = () => {
    if (contactToDelete !== null && contactToDelete > 0) {
      clearErrors([
        `contacts.${contactToDelete}.type`,
        `contacts.${contactToDelete}.firstName`,
        `contacts.${contactToDelete}.lastName`,
        `contacts.${contactToDelete}.mobile`,
        `contacts.${contactToDelete}.email`,
      ]);

      removeContact(contactToDelete);
      setDeleteDialogOpen(false);
      setContactToDelete(null);
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setContactToDelete(null);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{isUnderageForm ? t('page.contact.title.parental') : t('page.contact.title.emergency')}</CardTitle>
        <CardDescription>
          {isUnderageForm ? t('page.contact.description.parental') : t('page.contact.description.emergency')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {contacts.length > 1 ? (
          <Accordion type="single" collapsible className="w-full" value={openContact} onValueChange={setOpenContact}>
            {contacts.map((contact, index) => (
              <AccordionItem key={index} value={`contact-${index}`}>
                <AccordionTrigger className="group">
                  <div className="flex grow items-center justify-between">
                    <span>
                      {contact.firstName && contact.lastName
                        ? `${contact.firstName} ${contact.lastName} (${t(`page.contact.type.${contact.type}`)})`
                        : t('page.contact.number', { count: index + 1 })}
                    </span>
                    {index > 0 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="ml-2 h-6 w-6 opacity-70 hover:opacity-100 hover:bg-destructive/10 hover:text-destructive"
                        onClick={e => handleRemoveContact(index, e)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <Label htmlFor={`contacts.${index}.type`} className="flex items-center">
                        {t('page.contact.label.type')} <span className="text-destructive ml-1">*</span>
                      </Label>
                      <Select
                        value={contact.type}
                        onValueChange={value => handleUpdateContact(index, 'type', value)}
                        disabled={isUnderageForm && index === 0}
                      >
                        <SelectTrigger id={`contacts.${index}.type`} className="w-full">
                          <SelectValue placeholder={t('page.contact.type.placeholder')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={EventRegistrationContactType.PARENT}>
                            {t('page.contact.type.PARENT')}
                          </SelectItem>
                          <SelectItem value={EventRegistrationContactType.PARTNER}>
                            {t('page.contact.type.PARTNER')}
                          </SelectItem>
                          <SelectItem value={EventRegistrationContactType.GRAND_PARENT}>
                            {t('page.contact.type.GRAND_PARENT')}
                          </SelectItem>
                          <SelectItem value={EventRegistrationContactType.RELATIVE}>
                            {t('page.contact.type.RELATIVE')}
                          </SelectItem>
                          <SelectItem value={EventRegistrationContactType.WELL_KNOWN}>
                            {t('page.contact.type.WELL_KNOWN')}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`contacts.${index}.firstName`} className="flex items-center">
                        {t('page.contact.label.first-name')} <span className="text-destructive ml-1">*</span>
                      </Label>
                      <Input
                        id={`contacts.${index}.firstName`}
                        {...register(`contacts.${index}.firstName`)}
                        value={contact.firstName}
                        onChange={e => handleUpdateContact(index, 'firstName', e.target.value)}
                        className={errors.contacts?.[index]?.firstName ? 'border-destructive' : ''}
                      />
                      {errors.contacts?.[index]?.firstName && (
                        <p className="text-sm text-destructive">{errors.contacts?.[index]?.firstName?.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`contacts.${index}.lastName`} className="flex items-center">
                        {t('page.contact.label.last-name')} <span className="text-destructive ml-1">*</span>
                      </Label>
                      <Input
                        id={`contacts.${index}.lastName`}
                        {...register(`contacts.${index}.lastName`)}
                        value={contact.lastName}
                        onChange={e => handleUpdateContact(index, 'lastName', e.target.value)}
                        className={errors.contacts?.[index]?.lastName ? 'border-destructive' : ''}
                      />
                      {errors.contacts?.[index]?.lastName && (
                        <p className="text-sm text-destructive">{errors.contacts?.[index]?.lastName?.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`contacts.${index}.mobile`} className="flex items-center">
                        {t('page.contact.label.phone')} <span className="text-destructive ml-1">*</span>
                      </Label>
                      <Input
                        id={`contacts.${index}.mobile`}
                        type="tel"
                        {...register(`contacts.${index}.mobile`)}
                        value={contact.mobile}
                        onChange={e => handleUpdateContact(index, 'mobile', e.target.value)}
                        className={errors.contacts?.[index]?.mobile ? 'border-destructive' : ''}
                      />
                      {errors.contacts?.[index]?.mobile && (
                        <p className="text-sm text-destructive">{errors.contacts?.[index]?.mobile?.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`contacts.${index}.email`} className="flex items-center">
                        {t('page.contact.label.email')}{' '}
                        {contact.type === EventRegistrationContactType.PARENT && (
                          <span className="text-destructive ml-1">*</span>
                        )}
                      </Label>
                      <Input
                        id={`contacts.${index}.email`}
                        type="email"
                        {...register(`contacts.${index}.email`)}
                        value={contact.email}
                        onChange={e => handleUpdateContact(index, 'email', e.target.value)}
                        className={errors.contacts?.[index]?.email ? 'border-destructive' : ''}
                      />
                      {errors.contacts?.[index]?.email && (
                        <p className="text-sm text-destructive">{errors.contacts?.[index]?.email?.message}</p>
                      )}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contacts.0.type" className="flex items-center">
                {t('page.contact.label.type')} <span className="text-destructive ml-1">*</span>
              </Label>
              <Select
                value={contacts[0]?.type}
                onValueChange={value => handleUpdateContact(0, 'type', value)}
                disabled={isUnderageForm}
              >
                <SelectTrigger id="contacts.0.type" className="w-full">
                  <SelectValue placeholder={t('page.contact.type.placeholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={EventRegistrationContactType.PARENT}>{t('page.contact.type.PARENT')}</SelectItem>
                  <SelectItem value={EventRegistrationContactType.PARTNER}>{t('page.contact.type.PARTNER')}</SelectItem>
                  <SelectItem value={EventRegistrationContactType.GRAND_PARENT}>
                    {t('page.contact.type.GRAND_PARENT')}
                  </SelectItem>
                  <SelectItem value={EventRegistrationContactType.RELATIVE}>
                    {t('page.contact.type.RELATIVE')}
                  </SelectItem>
                  <SelectItem value={EventRegistrationContactType.WELL_KNOWN}>
                    {t('page.contact.type.WELL_KNOWN')}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contacts.0.firstName" className="flex items-center">
                {t('page.contact.label.first-name')} <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="contacts.0.firstName"
                {...register('contacts.0.firstName')}
                value={contacts[0]?.firstName}
                onChange={e => handleUpdateContact(0, 'firstName', e.target.value)}
                className={errors.contacts?.[0]?.firstName ? 'border-destructive' : ''}
              />
              {errors.contacts?.[0]?.firstName && (
                <p className="text-sm text-destructive">{errors.contacts?.[0]?.firstName?.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contacts.0.lastName" className="flex items-center">
                {t('page.contact.label.last-name')} <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="contacts.0.lastName"
                {...register('contacts.0.lastName')}
                value={contacts[0]?.lastName}
                onChange={e => handleUpdateContact(0, 'lastName', e.target.value)}
                className={errors.contacts?.[0]?.lastName ? 'border-destructive' : ''}
              />
              {errors.contacts?.[0]?.lastName && (
                <p className="text-sm text-destructive">{errors.contacts?.[0]?.lastName?.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contacts.0.mobile" className="flex items-center">
                {t('page.contact.label.phone')} <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="contacts.0.mobile"
                type="tel"
                {...register('contacts.0.mobile')}
                value={contacts[0]?.mobile}
                onChange={e => handleUpdateContact(0, 'mobile', e.target.value)}
                className={errors.contacts?.[0]?.mobile ? 'border-destructive' : ''}
              />
              {errors.contacts?.[0]?.mobile && (
                <p className="text-sm text-destructive">{errors.contacts?.[0]?.mobile?.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contacts.0.email" className="flex items-center">
                {t('page.contact.label.email')}{' '}
                {contacts[0]?.type === EventRegistrationContactType.PARENT && (
                  <span className="text-destructive ml-1">*</span>
                )}
              </Label>
              <Input
                id="contacts.0.email"
                type="email"
                {...register('contacts.0.email')}
                value={contacts[0]?.email}
                onChange={e => handleUpdateContact(0, 'email', e.target.value)}
                className={errors.contacts?.[0]?.email ? 'border-destructive' : ''}
              />
              {errors.contacts?.[0]?.email && (
                <p className="text-sm text-destructive">{errors.contacts?.[0]?.email?.message}</p>
              )}
            </div>
          </div>
        )}

        <Button variant="outline" className="w-full flex items-center justify-center gap-2" onClick={handleAddContact}>
          <Plus className="h-4 w-4" />
          {t('contact.add-another')}
        </Button>

        <FormNavigation nextDisabled={!areRequiredFieldsFilled || Object.keys(errors).length > 0} />
      </CardContent>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('contact.delete.title')}</DialogTitle>
            <DialogDescription>{t('contact.delete.description')}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDelete}>
              {t('shared.navigation.cancel')}
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              {t('shared.navigation.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
